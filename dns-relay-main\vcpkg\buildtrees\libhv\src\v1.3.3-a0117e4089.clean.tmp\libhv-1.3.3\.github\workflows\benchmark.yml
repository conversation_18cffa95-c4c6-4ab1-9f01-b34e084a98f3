name: benchmark

on:
  push:
    paths-ignore:
      - '**.md'

  pull_request:
    paths-ignore:
      - '**.md'

jobs:
  benchmark:
    name: benchmark
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: prepare
        run: |
          sudo apt update
          sudo apt install libssl-dev
          sudo apt install iperf
          sudo apt install nginx

      - name: make examples
        run: |
          ./configure
          make examples

      - name: build echo-servers
        run: |
          bash echo-servers/build.sh

      - name: benchmark echo-servers
        run: |
          bash echo-servers/benchmark.sh

      - name: benchmark tcp_proxy_server
        run: |
          iperf -s -p 5001 > /dev/null &
          bin/tcp_proxy_server 1212 127.0.0.1:5001 &
          iperf -c 127.0.0.1 -p 5001 -l 8K
          iperf -c 127.0.0.1 -p 1212 -l 8K

      - name: webbench
        run: |
          sudo nginx -c /etc/nginx/nginx.conf
          sudo cp html/index.html index.html
          sudo cp html/index.html /var/www/html/index.html
          bin/httpd -c etc/httpd.conf -d
          ps aux | grep nginx
          ps aux | grep httpd
          bin/tinyhttpd 9090 &
          echo -e "\n======Test nginx send file==============================="
          bin/wrk -c 100 -t 2 -d 10s http://127.0.0.1:80/
          echo -e "\n======Test libhv/httpd send file (with FileCache)========"
          bin/wrk -c 100 -t 2 -d 10s http://127.0.0.1:8080/
          echo -e "\n======Test libhv/tinyhttpd send file (no FileCache)======"
          bin/wrk -c 100 -t 2 -d 10s http://127.0.0.1:9090/
          echo -e "\n======Test nginx 404 Not Found==========================="
          bin/wrk -c 100 -t 2 -d 10s http://127.0.0.1:80/404
          echo -e "\n======Test libhv/httpd 404 Not Found====================="
          bin/wrk -c 100 -t 2 -d 10s http://127.0.0.1:8080/404
          echo -e "\n======Test libhv/tinyhttpd 404 Not found================="
          bin/wrk -c 100 -t 2 -d 10s http://127.0.0.1:9090/404
          echo -e "\n======Test libhv/httpd /ping============================="
          bin/wrk -c 100 -t 2 -d 10s http://127.0.0.1:8080/ping
          echo -e "\n======Test libhv/tinyhttpd /ping========================="
          bin/wrk -c 100 -t 2 -d 10s http://127.0.0.1:9090/ping
