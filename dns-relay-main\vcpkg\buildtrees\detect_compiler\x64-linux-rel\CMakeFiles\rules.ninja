# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.30

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: detect_compiler
# Configurations: Release
# =============================================================================
# =============================================================================

#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/downloads/tools/cmake-3.30.1-linux/cmake-3.30.1-linux-x86_64/bin/cmake --regenerate-during-build -S/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/scripts/detect_compiler -B/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/detect_compiler/x64-linux-rel
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/downloads/tools/ninja/1.12.1-linux/ninja $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/downloads/tools/ninja/1.12.1-linux/ninja -t targets
  description = All primary targets available:

