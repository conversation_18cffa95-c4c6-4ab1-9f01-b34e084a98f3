cmake 3.30.1
features core
portfile.cmake 0af6a39492199bc7ce318a3a5f530e5c6a80811e72884c08ef8288392f4be950
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 3600011444071beb27b81ff82b3c866e3d4054636e3011609deb313d2baf57fd-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1211256efcbee06e9d815c047677831b91cd50b0
vcpkg-cmake c11d8aa1071fb205926030998cf9c1d023b6719ff8a0fa6d9bcf62a1a9ec2a10
vcpkg-cmake-config b3e6227a9c46d5cefacb0e63db4ce64c7509717f60edb2a6480b5feb7d594ac0
vcpkg.json f41e5b81aa2ed8fb7d95b05680f7fc7ce3d0e108f854aa41c71b92f2b2e7e6c7
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
