-- Configuring x64-linux-rel
-- The C compiler identification is GNU 13.3.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- The CXX compiler identification is GNU 13.3.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Configuring done (17.5s)
-- Generating done (0.4s)
-- Build files have been written to: /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/detect_compiler/x64-linux-rel

#COMPILER_HASH#1211256efcbee06e9d815c047677831b91cd50b0
#COMPILER_C_HASH#560b01b6ffad8a6d8be857f99362fa9c38a8b6b7
#COMPILER_C_VERSION#13.3.0
#COMPILER_C_ID#GNU
#COMPILER_C_PATH#/usr/bin/cc
#COMPILER_CXX_HASH#037f33d4670b49936169356044fef0a42408d189
#COMPILER_CXX_VERSION#13.3.0
#COMPILER_CXX_ID#GNU
#COMPILER_CXX_PATH#/usr/bin/c++
CMake Warning:
  Manually-specified variables were not used by the project:

    BUILD_SHARED_LIBS
    CMAKE_INSTALL_BINDIR
    CMAKE_INSTALL_LIBDIR
    VCPKG_PLATFORM_TOOLSET
    VCPKG_SET_CHARSET_FLAG
    _VCPKG_ROOT_DIR



