[1/2] "/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/downloads/tools/cmake-3.30.1-linux/cmake-3.30.1-linux-x86_64/bin/cmake" -E chdir ".." "/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/downloads/tools/cmake-3.30.1-linux/cmake-3.30.1-linux-x86_64/bin/cmake" "/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/cargs/src/v1.2.0-b7a30e948a.clean" "-G" "Ninja" "-DCMAKE_BUILD_TYPE=Release" "-DCMAKE_INSTALL_PREFIX=/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/packages/cargs_x64-linux" "-DFETCHCONTENT_FULLY_DISCONNECTED=ON" "-DENABLE_TESTS=OFF" "-DCMAKE_MAKE_PROGRAM=/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/downloads/tools/ninja/1.12.1-linux/ninja" "-DCMAKE_SYSTEM_NAME=Linux" "-DBUILD_SHARED_LIBS=OFF" "-DVCPKG_CHAINLOAD_TOOLCHAIN_FILE=/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/scripts/toolchains/linux.cmake" "-DVCPKG_TARGET_TRIPLET=x64-linux" "-DVCPKG_SET_CHARSET_FLAG=ON" "-DVCPKG_PLATFORM_TOOLSET=external" "-DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON" "-DCMAKE_FIND_PACKAGE_NO_PACKAGE_REGISTRY=ON" "-DCMAKE_FIND_PACKAGE_NO_SYSTEM_PACKAGE_REGISTRY=ON" "-DCMAKE_INSTALL_SYSTEM_RUNTIME_LIBS_SKIP=TRUE" "-DCMAKE_VERBOSE_MAKEFILE=ON" "-DVCPKG_APPLOCAL_DEPS=OFF" "-DCMAKE_TOOLCHAIN_FILE=/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/scripts/buildsystems/vcpkg.cmake" "-DCMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION=ON" "-DVCPKG_CXX_FLAGS=" "-DVCPKG_CXX_FLAGS_RELEASE=" "-DVCPKG_CXX_FLAGS_DEBUG=" "-DVCPKG_C_FLAGS=" "-DVCPKG_C_FLAGS_RELEASE=" "-DVCPKG_C_FLAGS_DEBUG=" "-DVCPKG_CRT_LINKAGE=dynamic" "-DVCPKG_LINKER_FLAGS=" "-DVCPKG_LINKER_FLAGS_RELEASE=" "-DVCPKG_LINKER_FLAGS_DEBUG=" "-DVCPKG_TARGET_ARCHITECTURE=x64" "-DCMAKE_INSTALL_LIBDIR:STRING=lib" "-DCMAKE_INSTALL_BINDIR:STRING=bin" "-D_VCPKG_ROOT_DIR=/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg" "-D_VCPKG_INSTALLED_DIR=/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/installed" "-DVCPKG_MANIFEST_INSTALL=OFF"
-- The C compiler identification is GNU 13.3.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Configuring done (12.3s)
-- Generating done (0.3s)
CMake Warning:
  Manually-specified variables were not used by the project:

    FETCHCONTENT_FULLY_DISCONNECTED
    VCPKG_PLATFORM_TOOLSET
    VCPKG_SET_CHARSET_FLAG
    _VCPKG_ROOT_DIR


-- Build files have been written to: /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/cargs/x64-linux-rel
[2/2] "/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/downloads/tools/cmake-3.30.1-linux/cmake-3.30.1-linux-x86_64/bin/cmake" -E chdir "../../x64-linux-dbg" "/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/downloads/tools/cmake-3.30.1-linux/cmake-3.30.1-linux-x86_64/bin/cmake" "/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/cargs/src/v1.2.0-b7a30e948a.clean" "-G" "Ninja" "-DCMAKE_BUILD_TYPE=Debug" "-DCMAKE_INSTALL_PREFIX=/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/packages/cargs_x64-linux/debug" "-DFETCHCONTENT_FULLY_DISCONNECTED=ON" "-DENABLE_TESTS=OFF" "-DCMAKE_MAKE_PROGRAM=/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/downloads/tools/ninja/1.12.1-linux/ninja" "-DCMAKE_SYSTEM_NAME=Linux" "-DBUILD_SHARED_LIBS=OFF" "-DVCPKG_CHAINLOAD_TOOLCHAIN_FILE=/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/scripts/toolchains/linux.cmake" "-DVCPKG_TARGET_TRIPLET=x64-linux" "-DVCPKG_SET_CHARSET_FLAG=ON" "-DVCPKG_PLATFORM_TOOLSET=external" "-DCMAKE_EXPORT_NO_PACKAGE_REGISTRY=ON" "-DCMAKE_FIND_PACKAGE_NO_PACKAGE_REGISTRY=ON" "-DCMAKE_FIND_PACKAGE_NO_SYSTEM_PACKAGE_REGISTRY=ON" "-DCMAKE_INSTALL_SYSTEM_RUNTIME_LIBS_SKIP=TRUE" "-DCMAKE_VERBOSE_MAKEFILE=ON" "-DVCPKG_APPLOCAL_DEPS=OFF" "-DCMAKE_TOOLCHAIN_FILE=/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/scripts/buildsystems/vcpkg.cmake" "-DCMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION=ON" "-DVCPKG_CXX_FLAGS=" "-DVCPKG_CXX_FLAGS_RELEASE=" "-DVCPKG_CXX_FLAGS_DEBUG=" "-DVCPKG_C_FLAGS=" "-DVCPKG_C_FLAGS_RELEASE=" "-DVCPKG_C_FLAGS_DEBUG=" "-DVCPKG_CRT_LINKAGE=dynamic" "-DVCPKG_LINKER_FLAGS=" "-DVCPKG_LINKER_FLAGS_RELEASE=" "-DVCPKG_LINKER_FLAGS_DEBUG=" "-DVCPKG_TARGET_ARCHITECTURE=x64" "-DCMAKE_INSTALL_LIBDIR:STRING=lib" "-DCMAKE_INSTALL_BINDIR:STRING=bin" "-D_VCPKG_ROOT_DIR=/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg" "-D_VCPKG_INSTALLED_DIR=/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/installed" "-DVCPKG_MANIFEST_INSTALL=OFF"
-- The C compiler identification is GNU 13.3.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Configuring done (12.3s)
-- Generating done (0.3s)
CMake Warning:
  Manually-specified variables were not used by the project:

    FETCHCONTENT_FULLY_DISCONNECTED
    VCPKG_PLATFORM_TOOLSET
    VCPKG_SET_CHARSET_FLAG
    _VCPKG_ROOT_DIR


-- Build files have been written to: /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/cargs/x64-linux-dbg
