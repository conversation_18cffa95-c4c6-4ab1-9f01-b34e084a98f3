include(CheckIncludeFiles)
macro(check_header header)
    string(TOUPPER ${header} str1)
    string(REGEX REPLACE "[/.]" "_" str2 ${str1})
    set(str3 HAVE_${str2})
    check_include_files(${header} ${str3})
    if (${str3})
        set(${str3} 1)
    else()
        set(${str3} 0)
    endif()
endmacro()

include(CheckSymbolExists)
macro(check_function function header)
    string(TOUPPER ${function} str1)
    set(str2 HAVE_${str1})
    check_symbol_exists(${function} ${header} ${str2})
    if (${str2})
        set(${str2} 1)
    else()
        set(${str2} 0)
    endif()
endmacro()

macro(list_source_directories srcs)
    unset(tmp)
    foreach(dir ${ARGN})
        aux_source_directory(${dir} tmp)
    endforeach()
    set(${srcs} ${tmp})
    list(FILTER ${srcs} EXCLUDE REGEX ".*_test\\.c")
endmacro()

macro(glob_headers_and_sources files)
    unset(tmp)
    foreach(dir ${ARGN})
        file(GLOB tmp ${dir}/*.h ${dir}/*.c ${dir}/*.hpp ${dir}/*.cpp)
        list(APPEND ${files} ${tmp})
    endforeach()
    list(FILTER ${files} EXCLUDE REGEX ".*_test\\.c")
endmacro()
