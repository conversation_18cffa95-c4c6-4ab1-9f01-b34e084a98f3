Downloading https://github.com/likle/cargs/archive/v1.2.0.tar.gz -> likle-cargs-v1.2.0.tar.gz
Successfully downloaded likle-cargs-v1.2.0.tar.gz
-- Extracting source /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/downloads/likle-cargs-v1.2.0.tar.gz
-- Using source at /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/cargs/src/v1.2.0-b7a30e948a.clean
-- Configuring x64-linux
-- Building x64-linux-dbg
-- Building x64-linux-rel
-- Fixing pkgconfig file: /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/packages/cargs_x64-linux/lib/pkgconfig/cargs.pc
-- Fixing pkgconfig file: /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/packages/cargs_x64-linux/debug/lib/pkgconfig/cargs.pc
-- Installing: /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/packages/cargs_x64-linux/share/cargs/copyright
-- Performing post-build validation
