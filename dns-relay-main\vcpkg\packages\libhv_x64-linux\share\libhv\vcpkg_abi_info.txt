cmake 3.30.1
features core
portfile.cmake 1eac4fe632434d317dbb55df22ccff90d6b69de0082152d0c39c9b1633e45d0a
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 3600011444071beb27b81ff82b3c866e3d4054636e3011609deb313d2baf57fd-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1211256efcbee06e9d815c047677831b91cd50b0
vcpkg-cmake c11d8aa1071fb205926030998cf9c1d023b6719ff8a0fa6d9bcf62a1a9ec2a10
vcpkg-cmake-config b3e6227a9c46d5cefacb0e63db4ce64c7509717f60edb2a6480b5feb7d594ac0
vcpkg.json 5cfb078ced6679f69ff2091ff1cd1f94f5f642e6d0335d3aa777cdd1bdf51082
vcpkg_check_features 943b217e0968d64cf2cb9c272608e6a0b497377e792034f819809a79e1502c2b
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
