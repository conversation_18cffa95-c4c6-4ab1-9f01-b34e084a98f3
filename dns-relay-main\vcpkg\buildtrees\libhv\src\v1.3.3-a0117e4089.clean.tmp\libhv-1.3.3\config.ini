# Don't modify this file, you should modify config.mk or
# run ./configure --with-MODULE --enable-FEATURE

PREFIX=/usr/local
INSTALL_INCDIR=$(PREFIX)/include/hv
INSTALL_LIBDIR=$(PREFIX)/lib

# modules
# include icmp dns ftp smtp
WITH_PROTOCOL=no

WITH_EVPP=yes
WITH_HTTP=yes
WITH_HTTP_SERVER=yes
WITH_HTTP_CLIENT=yes
WITH_MQTT=no

# features
# base/hsocket.h: Unix Domain Socket
ENABLE_UDS=no
# base/RAII.cpp: Windows MiniDumpWriteDump
ENABLE_WINDUMP=no
# http/http_content.h: KeyValue,QueryParams,MultiPart
USE_MULTIMAP=no

# dependencies
# for http/client
WITH_CURL=no
# for http2
WITH_NGHTTP2=no
# for SSL/TLS
WITH_OPENSSL=no
WITH_GNUTLS=no
WITH_MBEDTLS=no

# rudp
WITH_KCP=no
