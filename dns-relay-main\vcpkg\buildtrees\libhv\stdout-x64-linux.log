Downloading https://github.com/ithewei/libhv/archive/v1.3.3.tar.gz -> ithewei-libhv-v1.3.3.tar.gz
Successfully downloaded ithewei-libhv-v1.3.3.tar.gz
-- Extracting source /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/downloads/ithewei-libhv-v1.3.3.tar.gz
CMake Error at scripts/cmake/vcpkg_extract_source_archive.cmake:153 (file):
  file RENAME failed to rename

    /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/libhv/src/v1.3.3-a0117e4089.clean.tmp/libhv-1.3.3

  to

    /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/libhv/src/v1.3.3-a0117e4089.clean

  because: Permission denied

Call Stack (most recent call first):
  scripts/cmake/vcpkg_extract_source_archive_ex.cmake:8 (vcpkg_extract_source_archive)
  scripts/cmake/vcpkg_from_github.cmake:127 (vcpkg_extract_source_archive_ex)
  ports/libhv/portfile.cmake:1 (vcpkg_from_github)
  scripts/ports.cmake:206 (include)


