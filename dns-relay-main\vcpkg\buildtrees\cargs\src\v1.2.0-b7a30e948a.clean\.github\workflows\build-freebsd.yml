name: Build on FreeBSD
on:
  push:
    branches: [ "**" ]
  pull_request:
    branches: [ "**" ]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: build
      id: test
      uses: vmactions/freebsd-vm@v1
      with:
        usesh: true
        prepare: |
          pkg install -y cmake
        run: |
          cmake -B build -S ${{github.workspace}} -DENABLE_TESTS=1 -DENABLE_DEMO=1
          cmake --build build
          ctest --test-dir build
