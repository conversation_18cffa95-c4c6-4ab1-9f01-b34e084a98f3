Change Dir: '/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/cargs/x64-linux-rel'

Run Build Command(s): /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/downloads/tools/ninja/1.12.1-linux/ninja -v -v -j17 install
[1/3] /usr/bin/cc  -I/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/cargs/src/v1.2.0-b7a30e948a.clean/include -fPIC -O3 -DNDEBUG -std=gnu11 -Wall -Werror -Wextra -Wpedantic -MD -MT CMakeFiles/cargs.dir/src/cargs.c.o -MF CMakeFiles/cargs.dir/src/cargs.c.o.d -o CMakeFiles/cargs.dir/src/cargs.c.o -c '/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/cargs/src/v1.2.0-b7a30e948a.clean/src/cargs.c'
[2/3] : && /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/downloads/tools/cmake-3.30.1-linux/cmake-3.30.1-linux-x86_64/bin/cmake -E rm -f libcargs.a && /usr/bin/ar qc libcargs.a  CMakeFiles/cargs.dir/src/cargs.c.o && /usr/bin/ranlib libcargs.a && :
[2/3] cd /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/cargs/x64-linux-rel && /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/downloads/tools/cmake-3.30.1-linux/cmake-3.30.1-linux-x86_64/bin/cmake -P cmake_install.cmake
-- Install configuration: "Release"
-- Installing: /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/packages/cargs_x64-linux/lib/libcargs.a
-- Installing: /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/packages/cargs_x64-linux/include/cargs.h
-- Installing: /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/packages/cargs_x64-linux/lib/cmake/cargs/CargsConfig.cmake
-- Installing: /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/packages/cargs_x64-linux/lib/cmake/cargs/CargsConfigVersion.cmake
-- Installing: /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/packages/cargs_x64-linux/lib/pkgconfig/cargs.pc
-- Installing: /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/packages/cargs_x64-linux/lib/cmake/cargs/CargsTargets.cmake
-- Installing: /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/packages/cargs_x64-linux/lib/cmake/cargs/CargsTargets-release.cmake

