set(CMAKE_HOST_SYSTEM "Linux-********-microsoft-standard-WSL2")
set(CMAKE_HOST_SYSTEM_NAME "Linux")
set(CMAKE_HOST_SYSTEM_VERSION "********-microsoft-standard-WSL2")
set(CMAKE_HOST_SYSTEM_PROCESSOR "x86_64")

include("/mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/scripts/buildsystems/vcpkg.cmake")

set(CMAKE_SYSTEM "Linux")
set(CMAKE_SYSTEM_NAME "Linux")
set(CMAKE_SYSTEM_VERSION "")
set(CMAKE_SYSTEM_PROCESSOR "x86_64")

set(CMAKE_CROSSCOMPILING "OFF")

set(CMAKE_SYSTEM_LOADED 1)
