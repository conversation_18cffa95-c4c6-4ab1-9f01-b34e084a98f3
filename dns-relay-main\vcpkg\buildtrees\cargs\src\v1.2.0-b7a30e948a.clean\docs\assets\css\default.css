html,
body {
  height: 100%;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", <PERSON><PERSON>, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  margin: auto;
  padding: 20px;
  padding: 0;
  display: flex;
  flex-flow: column;
  background: #fafafa;
  color: #22162B;
}

header {
  text-align: right;
  margin-bottom: 20px;
  padding: 20px 20px;
  font-family: 'Ubuntu Mono';
  background: #451F55;
  color: #F8C630;
  border-bottom: 10px solid #724E91;
}

header .inner {
  max-width: 800px;
  margin: auto;
}

header .logo {
  float: left;
  vertical-align: middle;
  display: inline-block;
  font-weight: bold;
  font-size: 32pt;
  padding: 0;
  margin-left: 10px;
  line-height: 50px;
}

header nav {
  line-height: 50px;
}

header a {
  padding: 10px;
  text-decoration: none;
  font-weight: bold;
  font-size: 18pt;
  overflow: auto;
  color: #E54F6D;;
}

.main {
  flex: 1;
}

footer {
  text-align: center;
  margin-top: 50px;
  padding: 40px 20px;
  font-family: 'Ubuntu Mono';
  background: #22162B;
  color: #F8C630;
}


a {
  color: #E54F6D;
}

h1, h2 {
  font-family: 'Ubuntu Mono';
  padding-bottom: 5px;
  border-bottom: 1px solid rgba(255,255,255,.2);
}

.content {
  color: #49483e;
  padding-top: 20px;
  max-width: 800px;
  margin: auto;
}

.highlight {
  padding: 10px;
  margin: 0;
  overflow: auto;
  white-space: pre-wrap;
}

.highlighter-rouge {
  background: rgba(0,0,0,0.1);
}

table {
  border-collapse: collapse;
}

table td,
table th {
  border: 1px solid #DDDDDD;
  background: #ffffff;
  padding: 5px 8px;
}

table tr:nth-child(even) td {
  background: #f8f9fb;
}

@media (max-width: 1000px) {
  header {
    text-align: center;
  }
  
  header .logo {
    display: block;
    float: none;
    margin-bottom: 10px;
  }
  
  .content {
    padding: 0 20px;
  }
}
