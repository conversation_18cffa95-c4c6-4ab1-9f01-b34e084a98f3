# Contributing
All your contributions to **cargs** are very welcome! **cargs** is especially happy to
receive contributions like:

 * general **bugfixes**
 * simple **bug reports**
 * **proposing new features**
 * **questions** (there are no dumb questions)
  
## License
**Any contributions you make will be under the MIT Software License.**

In short, when you submit code changes, your submissions are understood to be 
under the same MIT License that covers the project. Feel free to contact the 
maintainers if that's a concern.

## How to report a bug?
You can just use the issue tracker to do so.

## How to propose a new feature?
You can just use the issue tracker to do so.

## How to submit a bug fix?
Just submit a pull-request! Try to make sure that the code style fits the 
surrounding code.

## How to submit a new feature?
You probably want to create an issue first to discuss the change. All 
pull-requests will be considered though! Just try to make sure that the code 
style fits the surrounding code.