Package: libhv:x64-linux@1.3.3

**Host Environment**

- Host: x64-linux
- Compiler: GNU 13.3.0
-    vcpkg-tool version: 2025-05-19-ece4c0f6b8fae9e94513d544c7aa753dd2c82337
    vcpkg-scripts version: 89dc8be6db 2025-05-31 (4 days ago)

**To Reproduce**

`vcpkg install libhv cargs`

**Failure logs**

```
Downloading https://github.com/ithewei/libhv/archive/v1.3.3.tar.gz -> ithewei-libhv-v1.3.3.tar.gz
Successfully downloaded ithewei-libhv-v1.3.3.tar.gz
-- Extracting source /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/downloads/ithewei-libhv-v1.3.3.tar.gz
CMake Error at scripts/cmake/vcpkg_extract_source_archive.cmake:153 (file):
  file RENAME failed to rename

    /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/libhv/src/v1.3.3-a0117e4089.clean.tmp/libhv-1.3.3

  to

    /mnt/c/Users/<USER>/Desktop/internet/dns-relay-main/vcpkg/buildtrees/libhv/src/v1.3.3-a0117e4089.clean

  because: Permission denied

Call Stack (most recent call first):
  scripts/cmake/vcpkg_extract_source_archive_ex.cmake:8 (vcpkg_extract_source_archive)
  scripts/cmake/vcpkg_from_github.cmake:127 (vcpkg_extract_source_archive_ex)
  ports/libhv/portfile.cmake:1 (vcpkg_from_github)
  scripts/ports.cmake:206 (include)



```

