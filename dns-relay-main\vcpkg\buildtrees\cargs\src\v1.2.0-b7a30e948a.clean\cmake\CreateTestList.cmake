function(create_test_list list_name file)
  set("TEST_LIST_FILE_${list_name}" ${file} PARENT_SCOPE)
  file(WRITE ${file} "#define UNIT_TESTS(XX) \\\n")
  file(APPEND ${file} ${TEST_LIST_CONTENT_${list_name}})
  file(APPEND ${file} "\n")
endfunction()

function(create_test list_name unit_name test_name)
  set(TEST_LIST_CONTENT_${list_name} "${TEST_LIST_CONTENT_${list_name}}  XX(${unit_name},${test_name}) \\\n" PARENT_SCOPE)
  add_test(NAME "${unit_name}_${test_name}" COMMAND ${TEST_TARGET} ${unit_name} ${test_name})
endfunction()