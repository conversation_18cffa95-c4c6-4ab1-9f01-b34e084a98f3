name: Build on Windows
on:
  push:
    branches: [ "**" ]
  pull_request:
    branches: [ "**" ]
jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [windows-2019, windows-2022]
        build_type: [Release]
        c_compiler: [cl]
    steps:
    - uses: actions/checkout@v3
    - name: Set reusable strings
      id: strings
      shell: bash
      run: |
        echo "build-output-dir=${{ github.workspace }}/build" >> "$GITHUB_OUTPUT"
    - name: Configure CMake
      run: >
        cmake -B ${{ steps.strings.outputs.build-output-dir }}
        -DCMAKE_CXX_COMPILER=${{ matrix.cpp_compiler }}
        -DCMAKE_C_COMPILER=${{ matrix.c_compiler }}
        -DCMAKE_BUILD_TYPE=${{ matrix.build_type }}
        -DCMAKE_C_FLAGS=/wd5105
        -DENABLE_TESTS=1
        -DENABLE_DEMO=1
        -S ${{ github.workspace }}
    - name: Build
      run: cmake --build ${{ steps.strings.outputs.build-output-dir }} --config ${{ matrix.build_type }}
    - name: Test
      working-directory: ${{ steps.strings.outputs.build-output-dir }}
      run: ctest --build-config ${{ matrix.build_type }}
