cmake 3.30.1
copyright 04b60f99a43bfd7fefdc8364b24aac704a2160cef969b75ba6a38b62dc4c4b70
features core
portfile.cmake 832b34e63f5af41ad1b2e4aa79c5bfa507a005b120b51548e674accc706837d7
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 3600011444071beb27b81ff82b3c866e3d4054636e3011609deb313d2baf57fd-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1211256efcbee06e9d815c047677831b91cd50b0
vcpkg-port-config.cmake 72bc3093337e633bdd19fe5d4dd1f38ca1918def49608d676a9c98c686d38b1e
vcpkg.json 3964c3d0e6b39c7f42cebd5949cccdc7eb6feb83ee434fe73756c954b173c5c2
vcpkg_cmake_config_fixup.cmake f3880578674f1bdbc54b67c5fc3787aaab8ee8bcf4e3e6e008204bf4ab67aa0b
vcpkg_list f5de3ebcbc40a4db90622ade9aca918e2cf404dc0d91342fcde457d730e6fa29
