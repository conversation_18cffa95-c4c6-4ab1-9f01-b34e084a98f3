# enable warnings
function(enable_warnings target)
  if(MSVC)
    target_compile_definitions(${target} PRIVATE _CRT_SECURE_NO_WARNINGS)
    target_compile_options(${target} PRIVATE /W4)
    target_compile_options(${target} PRIVATE /WX)
  elseif("${CMAKE_C_COMPILER_ID}" STREQUAL "Clang")
    target_compile_options(${target} PRIVATE -Werror)
    target_compile_options(${target} PRIVATE -Wall)
    target_compile_options(${target} PRIVATE -Wextra)
    target_compile_options(${target} PRIVATE -Wpedantic)
    target_compile_options(${target} PRIVATE -Wno-gnu-zero-variadic-macro-arguments)
  elseif("${CMAKE_C_COMPILER_ID}" STREQUAL "GNU")
    target_compile_options(${target} PRIVATE -Wall)
    target_compile_options(${target} PRIVATE -Werror)
    target_compile_options(${target} PRIVATE -Wextra)
    target_compile_options(${target} PRIVATE -Wpedantic)
  endif()
endfunction()