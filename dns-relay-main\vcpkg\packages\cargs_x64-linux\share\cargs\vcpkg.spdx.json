{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/cargs-x64-linux-1.2.0-89486d62-75ad-4d68-81bd-efb3000fb667", "name": "cargs:x64-linux@1.2.0 ff649be4ca77d2a8ef7061374cbb7ee06dff99fc41d429a8de9a571ba75c4f6a", "creationInfo": {"creators": ["Tool: vcpkg-2025-05-19-ece4c0f6b8fae9e94513d544c7aa753dd2c82337"], "created": "2025-06-04T13:22:23Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "cargs", "SPDXID": "SPDXRef-port", "versionInfo": "1.2.0", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/cargs", "homepage": "https://likle.github.io/cargs/", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "A lightweight cross-platform getopt alternative that works on Linux, Windows and macOS. Command line argument parser library for C/C++. Can be used to parse argv and argc parameters.", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "cargs:x64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "ff649be4ca77d2a8ef7061374cbb7ee06dff99fc41d429a8de9a571ba75c4f6a", "downloadLocation": "NONE", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "likle/cargs", "downloadLocation": "git+https://github.com/likle/cargs@v1.2.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "56877e330745369585b1b6ed274e8c898381439915048375a22a3fed077c1818b5d21356a33a77f516571d834a3fce7f78e509df63ce0f93b8276ac0a93df02a"}]}], "files": [{"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "0af6a39492199bc7ce318a3a5f530e5c6a80811e72884c08ef8288392f4be950"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "f41e5b81aa2ed8fb7d95b05680f7fc7ce3d0e108f854aa41c71b92f2b2e7e6c7"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}